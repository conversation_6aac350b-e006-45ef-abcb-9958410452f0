<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class DownloadTenantSignatureWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:tenantSignatureDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tenant Signature Detail Workflow';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    protected $headings = [
        "Building/Unit",
        "Member Name",
        "Email Address",
        "Mobile Number",
        "Member Type",
        "Signature"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Set parameters to get all records for the report
            $this->request['per_page'] = 100000;

            // Get data from tenantSignatureReport datasource
            $tenantSignatureData = $this->action('datasource:tenantSignatureReport', $this->pointer, $this->request);

            // Format the data for Excel/PDF generation
            if($tenantSignatureData && count($tenantSignatureData) > 0)
            {
                $formattedData = [];

                foreach($tenantSignatureData as $item)
                {
                    $formattedData[] = [
                        'unit_name' => $item['unit_name'] ?? '',
                        'member_name' => $item['member_name'] ?? '',
                        'member_email_id' => $item['member_email_id'] ?? '',
                        'member_mobile_number' => $item['member_mobile_number'] ?? '',
                        'member_type_name' => $item['member_type_name'] ?? '',
                        'member_signature' => $item['member_signature'] ?? ''
                    ];
                }

                // Generate Excel or PDF based on type
                if($type == 'excel')
                {
                    $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'tenant_signature_report_');
                    $this->data['url'] = $data['data'];
                }
                else if($type == 'pdf')
                {
                    $data = $this->hitCURLForGeneratePDF($formattedData, $this->headings, 'tenant_signature_report');
                    $this->data['url'] = $data['data'];
                }
                else
                {
                    $this->status = 'error';
                    $this->message = 'Invalid type. Please provide either excel or pdf';
                    $this->statusCode = 400;
                    $this->data = [];
                }
            }
            else
            {
                $this->status = 'error';
                $this->message = 'No data found for the report';
                $this->statusCode = 404;
                $this->data = [];
            }
        }
    }
}
