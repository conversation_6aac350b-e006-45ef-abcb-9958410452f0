<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class MemberAdvancesDownloadReport extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:advanceMemberReportDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Member Advance Download';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    protected $headings = [
        "Building/Unit",
        "Refundable Balance",
        "Adjustable Balance"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Set parameters to get all records for the report
            $this->request['per_page'] = 100000;

            // Get data from memberAdvance datasource
            $memberAdvanceData = $this->action('datasource:memberAdvance', $this->pointer, $this->request);

            // Check if data is in the expected format [data_array, summary_array]
            if($memberAdvanceData && is_array($memberAdvanceData) && count($memberAdvanceData) >= 2)
            {
                $mainData = $memberAdvanceData[0]; // Main data array
                $summaryData = $memberAdvanceData[1]; // Summary data array
                $formattedData = [];

                // Format the main data
                foreach($mainData as $item)
                {
                    $formattedData[] = [
                        'building_unit_name' => $item['building_unit_name'] ?? '',
                        'total_refundable' => $item['total_refundable'] ?? '0.00',
                        'total_adjustable' => $item['total_adjustable'] ?? '0.00'
                    ];
                }

                // Add Total row from summary data
                if(!empty($summaryData) && isset($summaryData[0]))
                {
                    $totalRow = [
                        'building_unit_name' => 'Total',
                        'total_refundable' => $summaryData[0]['total_summary_refundable'] ?? '0',
                        'total_adjustable' => $summaryData[0]['total_summary_adjustable'] ?? '0'
                    ];
                    $formattedData[] = $totalRow;
                }

                // Generate Excel or PDF based on type
                if($type == 'excel')
                {
                    $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'member_advances_report_');
                    $this->data['url'] = $data['data'];
                }
                else if($type == 'pdf')
                {
                    $data = $this->hitCURLForGeneratePDF($formattedData, $this->headings, 'member_advances_report');
                    $this->data['url'] = $data['data'];
                }
                else
                {
                    $this->status = 'error';
                    $this->message = 'Invalid type. Please provide either excel or pdf';
                    $this->statusCode = 400;
                    $this->data = [];
                }
            }
            else
            {
                $this->status = 'error';
                $this->message = 'No data found for the report';
                $this->statusCode = 404;
                $this->data = [];
            }
        }
    }
}
