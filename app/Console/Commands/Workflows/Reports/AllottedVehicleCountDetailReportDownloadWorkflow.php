<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class AllottedVehicleCountDetailReportDownloadWorkflow extends Workflow
{
    use CommonFunctionTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allottedVehicleCountDetailReportDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allotted Vehicle Count Detail Report Download';

    protected $headings = [
        "Building/Unit",
        "Shaded Parking",
        "Open Parking", 
        "2wheeler (Owner|Tenant)",
        "4wheeler (Owner|Tenant)"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Get data from allottedVehicleCount datasource
            $vehicleCountData = $this->action('datasource:allottedVehicleCount', $this->pointer, $this->request);

            // Format the data for Excel/PDF generation
            $formattedData = [];
            $totalShadedParking = 0;
            $totalOpenParking = 0;
            $totalTwoWheelerOwner = 0;
            $totalTwoWheelerTenant = 0;
            $totalFourWheelerOwner = 0;
            $totalFourWheelerTenant = 0;

            foreach($vehicleCountData as $item) {
                // Parse the two_wheeler_owner_tenant field (format: "owner|tenant")
                $twoWheelerParts = explode('|', $item['two_wheeler_owner_tenant'] ?? '0|0');
                $twoWheelerOwner = $twoWheelerParts[0] ?? 0;
                $twoWheelerTenant = $twoWheelerParts[1] ?? 0;
                
                // Parse the four_wheeler_owner_tenant field (format: "owner|tenant")
                $fourWheelerParts = explode('|', $item['four_wheeler_owner_tenant'] ?? '0|0');
                $fourWheelerOwner = $fourWheelerParts[0] ?? 0;
                $fourWheelerTenant = $fourWheelerParts[1] ?? 0;

                // For now, we'll set shaded parking to 0 as it's not in the current data structure
                $shadedParking = 0;
                $openParking = $item['open_parking'] ?? 0;

                $formattedData[] = [
                    'building_unit' => $item['building_unit'] ?? '',
                    'shaded_parking' => $shadedParking,
                    'open_parking' => $openParking,
                    'two_wheeler_owner_tenant' => $twoWheelerOwner . '|' . $twoWheelerTenant,
                    'four_wheeler_owner_tenant' => $fourWheelerOwner . '|' . $fourWheelerTenant
                ];

                // Calculate totals for summary
                $totalShadedParking += $shadedParking;
                $totalOpenParking += $openParking;
                $totalTwoWheelerOwner += $twoWheelerOwner;
                $totalTwoWheelerTenant += $twoWheelerTenant;
                $totalFourWheelerOwner += $fourWheelerOwner;
                $totalFourWheelerTenant += $fourWheelerTenant;
            }

            $this->data = [];

            if($type == 'excel')
            {
                // Add empty row before summary
                $formattedData[] = [
                    'building_unit' => '',
                    'shaded_parking' => '',
                    'open_parking' => '',
                    'two_wheeler_owner_tenant' => '',
                    'four_wheeler_owner_tenant' => ''
                ];
                
                // Add summary row
                $formattedData[] = [
                    'building_unit' => 'Total',
                    'shaded_parking' => $totalShadedParking,
                    'open_parking' => $totalOpenParking,
                    'two_wheeler_owner_tenant' => $totalTwoWheelerOwner . '|' . $totalTwoWheelerTenant,
                    'four_wheeler_owner_tenant' => $totalFourWheelerOwner . '|' . $totalFourWheelerTenant
                ];
                
                $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'allottedVehicleCountDetailReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                // For PDF, we need to structure the data differently
                $pdfData = [
                    $formattedData, // Main data
                    [[ // Summary data
                        'Total' => $totalOpenParking + $totalShadedParking,
                        'Shaded' => $totalShadedParking,
                        'Open' => $totalOpenParking,
                        '2Wheeler_Owner' => $totalTwoWheelerOwner,
                        '2Wheeler_Tenant' => $totalTwoWheelerTenant,
                        '4Wheeler_Owner' => $totalFourWheelerOwner,
                        '4Wheeler_Tenant' => $totalFourWheelerTenant
                    ]]
                ];
                
                $data = $this->hitCURLForGeneratePDF($pdfData, $this->headings, 'allottedVehicleCountDetailReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
