<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

// STEP 4: Create a workflow with same name specified in controller. Extend the Workflow class.
class ParkingAllotmentDetailReportDownloadWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:parkingAllottmentDetailReportPrintDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parking Allotment Detail Report Download';

    protected $rules = [];
    protected $rulesMessage = [];

    protected $formatterByKeys = ["id"];

    protected $headings = [
        'Parking Unit',
        'Parking Type',
        'Parking For',
        'Allotted To',
        'Primary Member',
        'Status'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Set parameters to get all records for the report
            $this->request['per_page'] = 100000;

            // Get data from parkingAllotmentList datasource
            $parkingAllotmentData = $this->action('datasource:parkingAllotmentList', $this->pointer, $this->request);

            // Format the data for Excel/PDF generation
            if($parkingAllotmentData && count($parkingAllotmentData) > 0)
            {
                $formattedData = [];

                foreach($parkingAllotmentData as $item)
                {
                    $formattedData[] = [
                        'parking_unit_name' => $item['parking_unit_name'] ?? '',
                        'unit_parking_type' => ucfirst(str_replace('_', ' ', $item['unit_parking_type'] ?? '')),
                        'allotment_for' => $item['allotment_for'] ?? '',
                        'allotment_to' => $item['allotment_to'] ?? '',
                        'member_primary_name' => $item['member_primary_name'] ?? '',
                        'status' => ucfirst($item['status'] ?? '')
                    ];
                }

                // Generate Excel or PDF based on type
                if($type == 'excel')
                {
                    $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'parking_allotment_detail_report_');
                    $this->data['url'] = $data['data'];
                }
                else if($type == 'pdf')
                {
                    $data = $this->hitCURLForGeneratePDF($formattedData, $this->headings, 'parking_allotment_detail_report');
                    $this->data['url'] = $data['data'];
                }
                else
                {
                    $this->status = 'error';
                    $this->message = 'Invalid type. Please provide either excel or pdf';
                    $this->statusCode = 400;
                    $this->data = [];
                }
            }
            else
            {
                $this->status = 'error';
                $this->message = 'No data found for the report';
                $this->statusCode = 404;
                $this->data = [];
            }
        }
    }
}
