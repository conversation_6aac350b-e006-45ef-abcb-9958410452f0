<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class HelpdeskReportDownloadWorkflow extends Workflow
{
    use CommonFunctionTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:helpdeskReportDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Helpdesk report Download';

    protected $headings = [
        "Ticket No.",
        "Date",
        "Subject",
        "From",
        "Assigned to",
        "Priority",
        "Status"
    ];
    protected $header = ['ticket_no' => 'Ticket No.'
    ];

    protected $summaryHeadings = [
        "Status",
        "Count"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $helpdeskReport = $this->action('datasource:helpdeskReport', $this->pointer, $this->request);
            // Extract the report data (first element of the array)
            $reportData = $helpdeskReport[0] ?? [];

            // Extract the summary data (second element of the array)
            $summaryData = $helpdeskReport[1][0] ?? [];

            // Format the main report data for Excel/PDF generation
            $formattedData = [];
            foreach($reportData as $item) {
                $formattedData[] = [
                    'ticket_no' => $item['ticket_number'] ?? '',
                    'date' => $item['created_date'] ?? '',
                    'subject' => $item['title'] ?? '',
                    'from' => $item['raised_by_name'] ?? '',
                    'assigned_to' => $item['member_email_id'] ?? '',
                    'priority' => $item['priority'] ?? '',
                    'status' => $item['status'] ?? ''
                ];
            }

            // Format the summary data
            $formattedSummary = [];
            foreach($summaryData as $status => $count) {
                $formattedSummary[] = [
                    'ticket_no' => ucfirst($status),
                    'date' => '',
                    'subject' => '',
                    'from' => '',
                    'assigned_to' => '',
                    'priority' => '',
                    'status' => $count
                ];
            }

            $this->data = [];

            if($type == 'excel')
            {
                // Add empty row before summary
                $formattedData[] = [
                    'ticket_no' => '',
                    'date' => '',
                    'subject' => '',
                    'from' => '',
                    'assigned_to' => '',
                    'priority' => '',
                    'status' => ''
                ];
                
                // Add summary header
                $formattedData[] = [
                    'ticket_no' => 'Summary',
                    'date' => '',
                    'subject' => '',
                    'from' => '',
                    'assigned_to' => '',
                    'priority' => '',
                    'status' => ''
                ];
                
                // Add summary data
                $formattedData = array_merge($formattedData, $formattedSummary);
                
                $data = $this->hitCURLForGenerateCSV($formattedData, $this->headings, 'helpdeskReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($helpdeskReport, $this->headings, 'helpdeskReport', [], $this->summaryHeadings);
                $this->data['url'] = $data['data'];
            }
        }
    }
}
