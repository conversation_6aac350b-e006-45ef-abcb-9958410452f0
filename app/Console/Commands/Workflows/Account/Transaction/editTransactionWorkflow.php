<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class editTransactionWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:editTransaction {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Transaction Workflow';

    protected $rules = [
        "transaction_date" => "required|date|date_format:Y-m-d",
        "from_ledger" => "required|numeric|min:1",
        "transaction_amount_from" => "required|numeric|min:0",
        "selection_from" => "required|string|in:dr,cr",
        "memo_desc_from" => "required|string",
        "to_ledger" => "required|numeric|min:1",
        "transaction_amount_to" => "required|numeric|min:0",
        "to_transaction_type" => "required|string|in:dr,cr",
        "to_memo_desc" => "required|string",
    ];

    protected $rulesMessage = [
        "transaction_date.required" => "Transaction Date is required",
        "transaction_date.date" => "Transaction Date must be a date",
        "transaction_date.date_format" => "Transaction Date must be in the format Y-m-d",
        "from_ledger.required" => "From Ledger is required",
        "from_ledger.numeric" => "From Ledger must be numeric",
        "from_ledger.min" => "From Ledger must be greater than 0",
        "transaction_amount_from.required" => "Transaction Amount is required",
        "transaction_amount_from.numeric" => "Transaction Amount must be numeric",
        "transaction_amount_from.min" => "Transaction Amount min value is 0",
        "selection_from.required" => "Selection From is required",
        "selection_from.string" => "Selection From must be a string",
        "selection_from.in" => "Selection From must be either dr or cr",
        "memo_desc_from.required" => "Memo Description is required",
        "memo_desc_from.string" => "Memo Description must be a string",
        "to_ledger.required" => "To Ledger is required",
        "to_ledger.numeric" => "To Ledger must be numeric",
        "to_ledger.min" => "To Ledger must be greater than 0",
        "transaction_amount_to.required" => "Transaction Amount is required",
        "transaction_amount_to.numeric" => "Transaction Amount must be numeric",
        "transaction_amount_to.min" => "Transaction Amount min value is 0",
        "to_transaction_type.required" => "To Transaction Type is required",
        "to_transaction_type.string" => "To Transaction Type must be a string",
        "to_transaction_type.in" => "To Transaction Type must be either dr or cr",
        "to_memo_desc.required" => "To Memo Description is required",
        "to_memo_desc.string" => "To Memo Description must be a string",
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $data = $this->action('datasource:editTransaction', $this->pointer, $this->request);
        $this->data = $data;
    }
}
