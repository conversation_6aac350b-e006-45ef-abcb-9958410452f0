<?php

namespace App\Console\Commands\Workflows\Account;

use App\Console\Commands\Workflow;
use Illuminate\Console\Command;

class AssetsSettingDownloadWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:AssetsSettingDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Investments List Workflow';

    /**
     * Get table schema.
     */


    /**
     * Execute the console command.
     */
    public function apply()
    {

        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $assetsList = $this->action('datasource:assetsList', $this->pointer, $this->request);
            $this->data = $assetsList;
            $this->meta['schema'] = $this->schema;
            $count = 0;


            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($assetsList, $this->headings, 'staff_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($assetsList, $this->headings, 'staff_');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
