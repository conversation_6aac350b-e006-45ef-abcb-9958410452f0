<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AccountSetWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:AccountSet {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Financial Year Workflow';

    protected $formatterKey = 'id';

    /**
     * Get table schema.
     */
    protected $schema = [
        "table" => [
            "tableTitle" => "Account Closing",
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    "title" => "Sr No",
                    "key" => "id"
                ],
                [
                    "title" => "Financial Year",
                    "key" => "year_financial"
                ],
                [
                    "title" => "Start From",
                    "key" => "fy_start_date"
                ],
                [
                    "title" => "Ending On",
                    "key" => "fy_end_date"
                ],
                [
                    "title" => "Status",
                    "key" => "closed",
                    "options" => [
                        "1" => [
                            "title" => "Closed A/cs"
                        ],
                        "0" => [
                            "title" => "Open"
                        ]
                    ]
                ],
                [
                    "title" => "Closing Action",
                    "key" => "title",
                    // "href" => "/admin/accountsetting/accountclose/:closingyearid/:partoclose",

                    "type" => "link",
                    "form" => "closeAccountForm",
                    "hide_on" => [
                        "closed" => [
                            1
                        ]
                    ]
                ]
            ]
        ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $AccountSetTracker = $this->action('datasource:AccountSet', $this->pointer, $this->request);
        $this->data = $AccountSetTracker;
        $this->meta['schema'] = $this->schema;
    }


}
