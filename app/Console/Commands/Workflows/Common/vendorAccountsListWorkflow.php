<?php

namespace App\Console\Commands\Workflows\Common;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class vendorAccountsListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:vendorAccountsList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Data List of All Vendor Accounts';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $this->action('datasource:VendorAccountsList', $this->pointer, $this->request);
    }
}
