<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;

class editTransactionDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editTransaction {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Transaction DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;

        // first check transaction entry is exist or not in the database
        $from_txn_id = $request['id'];
        $fromLedgerData = ChsoneLedgerTransaction::where('txn_id', $from_txn_id)->first();

        // if transaction is not found then return error
        if (!$fromLedgerData) {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "No data found for the given transaction id";
            return;
        }

        // check if ledger id is exist or not from chsone_grp_ledger_tree table
        $ledger_id = $request['ledger_id'];
        $fromLedgerName = ChsoneGrpLedgerTree::where('ledger_account_id', $ledger_id)->first();

        if (!$fromLedgerName) {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "No data found for the given ledger id";
            return;
        }

        // now find out toLedgerData from the transaction where txn_from_id = from_txn_id
        $toLedgerData = ChsoneLedgerTransaction::where('txn_from_id', $from_txn_id)->first();
        $toLedgerData = $toLedgerData->toArray();
        $to_txn_id = $toLedgerData['ledger_account_id'];

        // now update the transaction
        $from_ledg_id = $request['from_ledger'];
        $date_posted = $request['transaction_date'];
        $txn_date = $request['transaction_date'];
        $txn_amt_from = $request['transaction_amount_from'];
        $narration_from = $request['memo_desc_from'];
        $txn_type_from = $request['selection_from'];
        $from_txn_id = $request['id'];
        $from_id = $this->_editTransaction($from_txn_id, $from_ledg_id, $txn_amt_from, $narration_from, $txn_date, $txn_type_from);

        $to_txn_id = $to_txn_id;
        $to_ledg_id = $request['to_ledger'];
        $txn_amt_to = $request['transaction_amount_to'];
        $narration_to = $request['to_memo_desc'];
        $txn_type_to = $request['to_transaction_type'];
        $to_id = $this->_editTransaction($to_txn_id, $to_ledg_id, $txn_amt_to, $narration_to, $txn_date, $txn_type_to);

        $flag = 0;
        if (isset($to_id) && $to_id != '') {
            $flag = 1;
        }
        if (isset($from_id) && $from_id != '') {
            $flag = 1;
        }
        if ($flag == 1) {
            $this->status = "success";
            $this->statusCode = 200;
            $this->message = "Ledger Transaction updated successfully";
        } else {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "Error in updating Ledger Transaction";
        }
    }

    public function _editTransaction($txn_id, $ledger_id, $txn_amt, $narration, $txn_date, $txn_type)
    {
        $soc_id = $this->input['company_id'];

        // Find the ledger account name
        $txn_ledger_name = ChsoneGrpLedgerTree::where('soc_id', $soc_id)
        ->where('ledger_account_id', $ledger_id)
        ->first();

        if (!$txn_ledger_name) {
            return false; // Return false if no ledger account is found
        }

         // Find the transaction
        $txn = ChsoneLedgerTransaction::where('soc_id', $soc_id)
        ->where('txn_id', $txn_id)
        ->first();

        if (!$txn) {
            return false; // Return false if no transaction is found
        }

        // Update the transaction details
        $txn->txn_id = $txn_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->transaction_type = $txn_type;
        $txn->transaction_amount = $txn_amt;
        $txn->memo_desc = $narration;
        $txn->ledger_account_name = $txn_ledger_name->ledger_account_name;

        // Save and return the transaction ID or false on failure
        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }
}
