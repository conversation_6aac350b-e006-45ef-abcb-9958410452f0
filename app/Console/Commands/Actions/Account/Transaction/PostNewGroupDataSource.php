<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use App\Models\Tenants\IncomeInvoiceSetting;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;
use Carbon\Carbon;
use App\Models\Tenants\ChsoneInvestmentsMaster;
use App\Models\Tenants\ChsoneAssetsCategory;
use App\Models\Tenants\ChsoneTaxCategory;
use App\Models\Tenants\ChsoneTaxClass;
use App\Models\Tenants\ChsoneVendorsMaster;
use App\Models\Tenants\TaxExemption;
use App\Models\Tenants\ChsoneBankRecoMaster;


class PostNewGroupDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:PostNewGroup {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post New Ledger Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try
        {
            $soc_id = $this->input['company_id'];
            $postedValues = request()->post();
            $postedValues['soc_id'] = $soc_id;
            
            // fecht lstParentAccount details
            $parent_id = $postedValues['lstParentAccount'] ?? $postedValues['parent_id'];
            $parentLedger = $this->tenantDB()->table('chsone_grp_ledger_tree')->where('ledger_account_id', $parent_id)->first();

            // check ledger_account_name is already exists or not
            $checkExists = ChsoneGrpLedgerTree::where('ledger_account_name', $postedValues['ledger_account_name'])->where('soc_id', $soc_id)->first();
            if($checkExists){
                $this->status = 'error';
                $this->message = $postedValues['ledger_account_name'].' ledger name already exists.';
                $this->statusCode = 400;
                return;
            }

            // fetch current financial year detail
            $arrCurrentFYDetailObj = new SocAccountFinancialYearMaster();
            $arrCurrentFYDetail = $arrCurrentFYDetailObj->getFYDetail($soc_id);
            $arrCurrentFYDetail = json_decode(json_encode($arrCurrentFYDetail), true);

            if(isset($postedValues['nature']) && strtolower($postedValues['nature']) == 'credit'){
                $postedValues['nature'] = 'cr';
            } else {
                $postedValues['nature'] = 'dr';
            }

            $chsoneGrpLedgerTreeObj = new ChsoneGrpLedgerTree();
            $chsoneGrpLedgerTreeObj->added_on = date('Y-m-d H:i:s');
            $chsoneGrpLedgerTreeObj->soc_id = $soc_id ?? '';
            $chsoneGrpLedgerTreeObj->ledger_start_date = $arrCurrentFYDetail['fy_start_date'];
            $chsoneGrpLedgerTreeObj->ledger_account_name = isset($postedValues['ledger_account_name']) ? $postedValues['ledger_account_name'] : (isset($postedValues['group_account_name']) ? $postedValues['group_account_name'] : '');
            $chsoneGrpLedgerTreeObj->nature_of_account = $postedValues['nature'] ?? '';
            $chsoneGrpLedgerTreeObj->parent_id = $postedValues['lstParentAccount'] ?? $postedValues['parent_id'] ?? '';
            $chsoneGrpLedgerTreeObj->status = 1;
            $chsoneGrpLedgerTreeObj->entity_type = 'group';
            $chsoneGrpLedgerTreeObj->created_by = $postedValues['user_id'] ?? '';
            $chsoneGrpLedgerTreeObj->report_head = $parentLedger->report_head;
            $chsoneGrpLedgerTreeObj->context = $parentLedger->context;
            $chsoneGrpLedgerTreeObj->behaviour = $parentLedger->behaviour;
            $chsoneGrpLedgerTreeObj->save();

            if($chsoneGrpLedgerTreeObj->save()) {
                // get last inserted id
                $last_inserted_data = $this->tenantDB()->table('chsone_grp_ledger_tree')->orderBy('ledger_account_id', 'desc')->first();

                // now insert the entry into chsone ledger transaction table
                $ledgerEntry = $this->ledgerEntry($last_inserted_data);

                if(!$ledgerEntry) {
                    $this->message = "Unable to insert ledger transaction";
                    $this->status = 'error';
                    $this->statusCode = 400;
                }

                $this->message = "Group Added Successfully";
                $this->status = 'success';
                $this->statusCode = 200;
            } else {
                $this->message = "Unable to add Group";
                $this->status = 'error';
                $this->statusCode = 400;
            }
        }
        catch(\Exception $e){
            $this->message = 'Error: '.$e->getMessage();
            dd($e->getMessage());
        }
    }

    public function ledgerEntry($last_inserted_data) 
    {
        $obj = $this->tenantDB()->table('chsone_ledger_transactions')
        ->insert([
            'soc_id' => $last_inserted_data->soc_id,
            'transaction_date' => $last_inserted_data->ledger_start_date,
            'ledger_account_id' => $last_inserted_data->ledger_account_id,
            'ledger_account_name' => $last_inserted_data->ledger_account_name,
            'voucher_type' => '',
            'voucher_reference_number' => '',
            'voucher_reference_id' => 0,
            'transaction_type' => $last_inserted_data->nature_of_account,
            'payment_mode' => '',
            'payment_reference' => '',
            'transaction_amount' => 0,
            'txn_from_id' => 0,
            'memo_desc' => 'entry for opening balance',
            'is_opening_balance' => 1,
            'is_reconciled' => 0,
            'is_cancelled' => 0,
            'created_by' => $last_inserted_data->created_by,
            'added_on' => date('Y-m-d H:i:s'),
            'value_date' => $last_inserted_data->ledger_start_date,
        ]);

        if($obj) {
            return true;
        } else {
            return false;
        }
    }    
}
