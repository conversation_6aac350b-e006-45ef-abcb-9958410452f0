<?php

namespace App\Console\Commands\Actions\Member;

use App\Http\Traits\NotificationTraits;
use Illuminate\Console\Command;
use App\Console\Commands\Action;
use SebastianBergmann\CodeCoverage\Report\Xml\Unit;
use Illuminate\Support\Facades\DB;
use App\Models\Tenants\IncomeInvoiceParticular;
use App\Models\Tenants\IncomeUnitInvoice as UnitInvoice;
use App\Models\Tenants\IncomeInvoicePayment;
use App\Models\Tenants\IncomeInvoiceAdjustment;
use App\Models\Tenants\ChsoneCreditAccount as CreditAccounts;
use Illuminate\Support\Facades\Route;
use Carbon\Carbon;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.

class FetchBalanceDuesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:get-primary-member {flowId} {parentId} {input}';

    use NotificationTraits;
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Primery Member List';

    // STEP 9: Specified formatter for your action. This will format your data as per your requirement.
    // Formatter will 3 level depth array. 1st level for outer array and 2nd level for inner array and so on.
    protected $formatter =  [
        'id' => '',
        'unit_category_id' => '',
        'unit_category' => '',
        'soc_building_name' => '',
        'unit_flat_number' => '',
        'soc_building_floor' => '',
        'member_id' => '',
        'member_first_name' => '',
        'member_last_name' => '',
        'member_mobile_number' => '',
        'member_email_id' => '',
        'gstin' => '',
        'total_advance' => '',
        'total_balance_due' => '',
        'member_name' => 'concat:member_first_name,member_last_name', // for customization of value her you specifeid the function name and pass the key of your data. Then define function in curren class.
        // 'rupees_disable'=>  '',
        // 'is_generatedOutstanding' => '',

    ];

    // STEP 10: Specified formatterByKeys for your action. This will format your data as per specified keys.
    // 1st key for outer array and 2nd key for inner array and so on.
    protected $formatterByKeys =  ['unit_id as id'];

    // STEP 11: Mapper helps to map the keys of your database query and formatter.
    // If you have filters, please specified thoes keys in mapper.
    protected $mapper = [
        'unit_number' => 'units.unit_flat_number',
        'building' => 'units.soc_building_name',
        'unit_id' => 'unit_id',
    ];

    /**
     * Execute the console command.
     */

    #[\Override]
    public function apply(): void
    {
        // STEP 12-A: If you requriment for custom logic for filter then store filter data in variable.
        try {
            $unit_flat_number = '';
            $soc_building_name = '';
            $unit_name =  '';
            $member_name = '';
            $soc_id = $this->input['company_id'];

            $page = $this->input['page'] ?? 1;

            $currentRoute = Route::current();

            // Get the route URI pattern (e.g., "member/register/{id}")
            $routeUri = $currentRoute->uri();

            if ($routeUri == 'api/admin/admindashboard/index/balance_dues') {
                $this->hugeData = true;
            }

            $per_page = empty($this->input['per_page']) ? ($this->hugeData ? 100000 : 10) : ($this->input['per_page']);
            $offset = ($page - 1) * $per_page;

            if (isset($this->input['filters'])) {

                $unit_flat_number = empty($this->input['filters']['unit_flat_number']) ? '' : $this->input['filters']['unit_flat_number'];
                unset($this->input['filters']['unit_flat_number']);

                $soc_building_name = empty($this->input['filters']['soc_building_name']) ? '' : $this->input['filters']['soc_building_name'];
                unset($this->input['filters']['soc_building_name']);

                $unit_name = empty($this->input['filters']['unit_name']) ? '' : $this->input['filters']['unit_name'];
                unset($this->input['filters']['unit_name']);

                $member_name = empty($this->input['filters']['member_name']) ? '' : $this->input['filters']['member_name'];
                unset($this->input['filters']['member_name']);
            }

            $obj = $this->tenantDB()->table('chsone_members_master AS memberMaster')
                ->select(
                    'units.unit_id as id',
                    DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number) as unit_name"),
                    'units.fk_unit_category_id as unit_category_id',
                    'units.unit_category',
                    'units.soc_building_floor',
                    DB::raw("CAST(units.chargeable AS UNSIGNED) as chargeable"),
                    'memberMaster.id as member_id',
                    DB::raw("CONCAT(memberMaster.member_first_name,' ',memberMaster.member_last_name) as member_name"),
                    DB::raw("CONCAT(memberMaster.member_first_name, ' ', memberMaster.member_last_name, ' (', units.soc_building_name, '-', units.unit_flat_number, ')') as display_member_name"),
                    'memberMaster.member_mobile_number',
                    'memberMaster.member_email_id',
                    DB::raw('1 as start_invoicing')
                )
                ->leftJoin('chsone_units_master AS units', 'units.unit_id', '=', 'memberMaster.fk_unit_id')
                ->leftJoin('chsone_member_type_master AS memberType', 'memberType.member_type_id', '=', 'memberMaster.member_type_id')
                ->where('memberType.member_type_name', 'Primary')
                ->where('memberMaster.status', 1)
                ->where('memberMaster.soc_id', $soc_id)
                ->where('units.is_allotted', '1');

            if ($unit_flat_number) {
                $obj = $obj->where('units.unit_flat_number', 'like', '%' . $unit_flat_number . '%');
            }

            if ($soc_building_name) {
                $obj = $obj->where('units.soc_building_name', 'like', '%' . $soc_building_name . '%');
            }

            if ($unit_name) {
                $obj = $obj->where(DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number)"), 'like', '%' . $unit_name . '%');
            }

            if ($member_name) {
                $obj = $obj->where(DB::raw("CONCAT(memberMaster.member_first_name, ' ', memberMaster.member_last_name)"), 'like', '%' . $member_name . '%');
            }

            $count = $obj->count();

            $obj = $obj->offset($offset);
            $obj = $obj->limit($per_page);
            $obj = $obj->orderBy('unit_id', 'asc');
            $result = $obj->get();
            $result = json_decode(json_encode($result), true);

            // Get all unit IDs for bulk processing
            $unitIds = array_column($result, 'id');
            // Bulk fetch invoice data for all units

            $invoicesData = $this->getBulkInvoicesData($unitIds, $soc_id);

            // Bulk fetch credit accounts data
            $creditAccountsData = $this->getBulkCreditAccountsData($unitIds);

            // Bulk fetch outstanding data
            $outstandingData = $this->getBulkOutstandingData($unitIds, $soc_id);

            foreach ($result as $key => $value) {
                $company_name = $this->input['additional_data']['society_name'];
                $result[$key]['company_name'] = $company_name;
                $unitId = $value['id'];

                // Get invoice data for this unit
                $InvoicesList = $invoicesData[$unitId] ?? [];
                // Calculate due amount (keep as numeric for calculations)
                $dueAmount = array_sum(array_column($InvoicesList, 'invoice_balance_due'));
                // Get sum of outstanding adjustments for this unit
                $outstandingAdjustment = $this->tenantDB()->table('income_invoice_adjustment')
                    ->where('fk_unit_id', $unitId)
                    ->where('adjustment_type', 'outstanding')
                    ->selectRaw('
                    COALESCE(SUM(principal_amount),0) as principal_sum,
                    COALESCE(SUM(interest_amount),0) as interest_sum,
                    COALESCE(SUM(delayed_payment_charges),0) as delayed_sum
                ')
                    ->first();

                $outstandingSum = ($outstandingAdjustment->principal_sum ?? 0);
                // + ($outstandingAdjustment->interest_sum ?? 0)
                // + ($outstandingAdjustment->delayed_sum ?? 0);
                // Add to due_amount

                $checkFirstInvoice = $this->tenantDB()->table('income_unit_invoices')
                    ->where('fk_unit_id', $unitId)
                    ->first();
                if (!empty($checkFirstInvoice) && in_array($checkFirstInvoice->status, ['generated', 'partialpaid'])) {
                    $dueAmount += $outstandingSum;
                }

                // Format the final due amount with exactly 2 decimal places
                $result[$key]['due_amount'] = number_format($dueAmount, 2, '.', '');

                // Calculate last invoice date and end date
                $result[$key]['last_invoice_date'] = (count($InvoicesList) > 0 && $InvoicesList[0]['created_date'])
                    ? date('Y-m-d', strtotime((string) $InvoicesList[0]['created_date']))
                    : '-';

                if ($result[$key]['last_invoice_date'] !== '-') {
                    $lastInvoiceDate = Carbon::parse($result[$key]['last_invoice_date']);
                    $endofthemonth = $lastInvoiceDate->endOfMonth()->toDateString();
                } else {
                    $endofthemonth = '-';
                }

                $result[$key]['end_date'] = $endofthemonth;


                // Get outstanding data for this unit
                $is_generatedOutstanding = $outstandingData[$unitId] ?? [
                    'count' => 0,
                    'is_generated' => null,
                    'advance_amount' => 0,
                    'outstanding_amount' => 0,
                    'is_already_generated' => 0,
                ];

                $result[$key]['is_generatedBill'] = ($is_generatedOutstanding['is_already_generated'] == 0) ? 1 : 0;
                $result[$key]['rupees_disable'] = $result[$key]['is_generatedBill'] != 0;

                // Calculate advances using bulk credit data
                $singleMemberDetail = [];
                if (!empty($is_generatedOutstanding['is_generated'])) {
                    $creditDetail = $creditAccountsData[$unitId] ?? ['credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => 0];
                    $singleMemberDetail['credit_detail'] = $creditDetail;
                } else {
                    $singleMemberDetail['credit_detail'] = ['credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => $is_generatedOutstanding['advance_amount']];
                    $singleMemberDetail['outstanding_amount'] = $is_generatedOutstanding['outstanding_amount'];
                }

                $result[$key]['advances'] = number_format((float)$singleMemberDetail['credit_detail']['remaining_amount'], 2, '.', '');


                $downloadFlag = count($InvoicesList) > 0 && isset($InvoicesList[0]['invoice_number']) ? 1 : 0;
                //dd($result[$key]['invoice_detail']);

                $viewAllInvoiceFlag = isset($result[$key]['due_amount']) && $result[$key]['due_amount'] > 0 ? 1 : 0;
                $viewAllInvoiceFlagForAdvance = isset($result[$key]['advances']) && $result[$key]['advances'] > 0 ? 1 : 0;
                $viewReceiptFlag = isset($result[$key]['due_amount']) && $result[$key]['due_amount'] > 0 ? 1 : 0;
                $result[$key]['downloadFlag'] = $downloadFlag;
                $result[$key]['viewReceiptFlag'] = $viewReceiptFlag;
                $result[$key]['viewAllInvoiceFlag'] = $viewAllInvoiceFlag;
                $result[$key]['viewAllInvoiceFlagForAdvance'] = $viewAllInvoiceFlagForAdvance;
                $result[$key]['invoice_number'] = $InvoicesList[0]['invoice_number'] ?? '';
                $result[$key]['latest_invoice_id'] = $InvoicesList[0]['unit_invoice_id'] ?? '';
                //unset($result[$key]['invoice_detail']);
                //unset($result[$key]['total_advance']);
                //}
            }
            //dd( $result );
            // STEP 13: Format your data as per your requirement.
            $this->data = $result;


            if ($routeUri == 'api/admin/admindashboard/index/balance_dues') {
                // Calculate total due amount sum
                $totalDueAmount = 0;
                foreach ($this->data as $member) {
                    $totalDueAmount += isset($member['due_amount']) ? $member['due_amount'] : 0;
                }
                $this->data =  $totalDueAmount;
            }
            $this->meta['pagination']['total'] = $count;
        } catch (\Exception $e) {
            dd($e);
        }
    }




    // STEP 14: Define your custom function here. This will call while format function call.

    public function concat(string $a, string $b): string
    {
        return $a . ' ' . $b;
    }





    /**
     * Bulk fetch invoice data for multiple units to avoid N+1 queries
     * @return mixed[]
     */
    private function getBulkInvoicesData(array $unitIds, $socId): array
    {
        if ($unitIds === []) {
            return [];
        }

        $invoices = $this->tenantDB()->table('income_unit_invoices AS unitInvoices')
            ->select(
                'unitInvoices.fk_unit_id as unit_id',
                'unitInvoices.unit_invoice_id',
                'unitInvoices.invoice_number',
                'unitInvoices.created_date',
                'unitInvoices.status',
                DB::raw("
                    CASE
                        WHEN
                        (
                            ROUND(
                                (
                                    SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) + unitInvoices.interest_amount +
                                    (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                                ) + unitInvoices.roundoff_amount
                                - unitInvoices.advance_amount
                                - (
                                    (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                    + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                )
                            , 2)
                        ) < 0
                        THEN 0
                        ELSE
                        ROUND(
                            (
                                SUM(particulars.amount) + SUM(particulars.tax_applicable) + SUM(particulars.tax_exemptions) + unitInvoices.interest_amount +
                                (SELECT IFNULL(SUM(tax_amount), 0) FROM chsone_tax_log WHERE invoice_number = unitInvoices.invoice_number)
                            ) + unitInvoices.roundoff_amount
                            - unitInvoices.advance_amount
                            - (
                                (SELECT IFNULL(SUM(payment_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(writeoff_amount), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(tds_deducted), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                                + (SELECT IFNULL(SUM(late_payment_charges), 0) FROM income_invoice_payment WHERE fk_unit_invoice_id = unitInvoices.unit_invoice_id)
                            )
                        , 2)
                    END AS invoice_balance_due
                ")
            )
            ->join('income_invoice_particular AS particulars', 'unitInvoices.unit_invoice_id', '=', 'particulars.fk_unit_invoice_id')
            ->whereIn('unitInvoices.fk_unit_id', $unitIds)
            ->whereIn('unitInvoices.status', ['generated', 'partialpaid'])
            ->groupBy('particulars.fk_unit_invoice_id')
            ->orderBy('unitInvoices.fk_unit_id')
            ->orderByDesc('unitInvoices.created_date')
            ->get();

        // Group invoices by unit_id
        $groupedInvoices = [];
        foreach ($invoices as $invoice) {
            $unitId = $invoice->unit_id;
            if (!isset($groupedInvoices[$unitId])) {
                $groupedInvoices[$unitId] = [];
            }
            $groupedInvoices[$unitId][] = (array) $invoice;
        }
        return $groupedInvoices;
    }

    /**
     * Bulk fetch credit accounts data for multiple units
     * @return mixed[]
     */
    private function getBulkCreditAccountsData(array $unitIds): array
    {
        if ($unitIds === []) {
            return [];
        }

        $currentDate = $this->getCurrentDate('database');

        $creditAccounts = $this->tenantDB()->table('chsone_credit_accounts')
            ->select(
                'account_id',
                DB::raw('SUM(CASE WHEN transaction_type = "cr" THEN amount ELSE 0 END) as credit_amount'),
                DB::raw('SUM(CASE WHEN transaction_type = "dr" THEN amount ELSE 0 END) as debit_amount')
            )
            ->whereIn('account_id', $unitIds)
            ->where('account_context', 'unit')
            ->where('use_credit', 'adjustable')
            ->where(function ($q) use ($currentDate): void {
                $q->where('use_credit_after', '<=', $currentDate)
                    ->orWhereNull('use_credit_after');
            })
            ->where(function ($q): void {
                $q->where('use_credit_for', 'maintenance')
                    ->orWhere('use_credit_for', 'both');
            })
            ->groupBy('account_id')
            ->get();

        $creditData = [];
        foreach ($creditAccounts as $account) {
            $remainingAmount = $account->credit_amount - $account->debit_amount;
            $creditData[$account->account_id] = [
                'credit_amount' => $account->credit_amount,
                'debit_amount' => $account->debit_amount,
                'remaining_amount' => max(0, $remainingAmount)
            ];
        }

        return $creditData;
    }

    /**
     * Bulk fetch outstanding data for multiple units
     * @return mixed[]
     */
    private function getBulkOutstandingData(array $unitIds, $socId): array
    {
        if ($unitIds === []) {
            return [];
        }

        // Get outstanding adjustments
        $outstandingAmts = $this->tenantDB()->table('income_invoice_adjustment')
            ->whereIn('fk_unit_id', $unitIds)
            ->where('soc_id', $socId)
            ->get()
            ->keyBy('fk_unit_id');

        // Get invoice counts
        $invoiceCounts = $this->tenantDB()->table('income_unit_invoices')
            ->select('fk_unit_id', DB::raw('COUNT(*) as count'))
            ->whereIn('fk_unit_id', $unitIds)
            ->where('soc_id', $socId)
            ->where('status', '!=', 'cancelled')
            ->groupBy('fk_unit_id')
            ->get()
            ->keyBy('fk_unit_id');

        $outstandingData = [];
        foreach ($unitIds as $unitId) {
            $outstandingAmt = $outstandingAmts->get($unitId);
            $invoiceCount = $invoiceCounts->get($unitId);

            $isAlreadyGenerated = $invoiceCount ? ($invoiceCount->count > 0 ? 1 : 0) : 0;

            $return = [
                'count' => 0,
                'is_generated' => null,
                'advance_amount' => 0,
                'outstanding_amount' => 0,
                'is_already_generated' => $isAlreadyGenerated,
            ];

            if ($outstandingAmt) {
                $return['count'] = 1;
                $return['is_generated'] = $outstandingAmt->is_generated;

                if (
                    empty($outstandingAmt->is_generated) &&
                    strtolower($outstandingAmt->adjustment_type) === 'advance' &&
                    strtolower($outstandingAmt->bill_type) === 'maintenance'
                ) {
                    $return['advance_amount'] = $outstandingAmt->principal_amount;
                }

                if (
                    empty($outstandingAmt->is_generated) &&
                    strtolower($outstandingAmt->adjustment_type) === 'outstanding' &&
                    strtolower($outstandingAmt->bill_type) === 'maintenance'
                ) {
                    $return['outstanding_amount'] = $outstandingAmt->principal_amount +
                        $outstandingAmt->interest_amount +
                        $outstandingAmt->delayed_payment_charges;
                }
            }

            $outstandingData[$unitId] = $return;
        }

        return $outstandingData;
    }
}
