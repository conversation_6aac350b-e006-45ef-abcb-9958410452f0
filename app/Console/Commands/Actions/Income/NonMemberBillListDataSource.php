<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;
use App\Models\Tenants\IncomeNonmemberBillPayment;
use App\Models\Tenants\NonMemberIncomePayment;
use Illuminate\Support\Facades\Route;

class NonMemberBillListDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:NonMemberBillList {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the non member bill list';


    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'nonmemberincome.nonmember_bill_id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {

        $currentRoute = Route::current();
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/admin/income-details/hugeincomenonmember') {
            $this->hugeData = true;
        }
        if ($routeUri == 'api/admin/admindashboard/index/balance_dues') {
                $this->hugeData = true;
        }
        $bill_number = '';
        $billed_name = '';
        $bill_date = '';
        $payment_status = '';
        $searchTerm = $this->input['filters']['search'] ?? '';

        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        if (isset($this->input['filters'])) {


            $bill_number = !empty($this->input['filters']['bill_number']) ? $this->input['filters']['bill_number'] : '';
            unset($this->input['filters']['bill_number']);


            $billed_name = !empty($this->input['filters']['billed_name']) ? $this->input['filters']['billed_name'] : '';
            unset($this->input['filters']['billed_name']);

            $bill_date = !empty($this->input['filters']['bill_date']) ? $this->input['filters']['bill_date'] : '';
            unset($this->input['filters']['bill_date']);

            $payment_status = isset($this->input['filters']['payment_status']) ? explode(',', $this->input['filters']['payment_status']) : [];
            unset($this->input['filters']['payment_status']);
        }


        $obj = $this->tenantDB()->table('income_nonmember_bills AS nonmemberincome')
            ->select(
                'nonmemberincome.booker_email_address',
                'nonmemberincome.booker_address',
                'nonmemberincome.hsn',
                'nonmemberincome.gstin',
                'nonmemberincome.nonmember_bill_id as nonmember_bill_id',
                'nonmember.nonmember_id as id',
                'nonmemberincome.soc_id',
                'nonmemberincome.billed_name',
                'nonmember.address',
                'nonmemberincome.bill_for',
                'nonmemberincome.tax_class_id',
                'nonmemberincome.bill_number',
                'nonmemberincome.bill_amount',
                'nonmemberincome.advance_amount',
                'nonmemberincome.credit_amount',
                'nonmemberincome.discount_amount',
                'nonmemberincome.total_taxes',
                'nonmemberincome.payment_status',
                'nonmemberincome.from_date',
                'nonmemberincome.end_date',
                'nonmemberincome.bill_from',
                'nonmemberincome.booker_mobile_number',
                'nonmemberincome.nonmember_id',
                'nonmemberincome.bill_date',
                DB::raw("
            CASE
                WHEN nonmemberincome.payment_status != 'cancelled'
                     AND nonmemberincome.bill_from = 'facility'
                THEN 1
                ELSE 0
            END AS cancel_disable
        "),
                DB::raw("
        CASE
            WHEN nonmemberincome.payment_status = 'paid'
                 OR nonmemberincome.payment_status = 'cancelled'
            THEN 1
            ELSE 0
        END AS payment_disable
    ")
            )
            ->join('chsone_nonmember_master AS nonmember', 'nonmember.nonmember_id', '=', 'nonmemberincome.nonmember_id')
            ->whereNot('nonmemberincome.payment_status', 'cancelled')
            ->orderBy('nonmemberincome.nonmember_bill_id', 'DESC');

        if ($bill_number) {
            $obj = $obj->where('nonmemberincome.bill_number', 'like', '%' . $bill_number . '%');
        }

        if ($billed_name) {
            $obj = $obj->where('nonmemberincome.billed_name', 'like', '%' . $billed_name . '%');
        }

        if ($bill_date) {
            $obj = $obj->where('nonmemberincome.bill_date', 'like', '%' . $bill_date . '%');
        }



        if ($payment_status) {
            $obj = $obj->whereIn('nonmemberincome.payment_status', $payment_status);
        }


        if ($searchTerm) {
            $obj->where(function ($q) use ($searchTerm) {
                $q->orWhere('nonmemberincome.booker_email_address', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.hsn', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.nonmember_bill_id', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.soc_id', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.billed_name', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.bill_for', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.tax_class_id', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.bill_number', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.bill_amount', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.advance_amount', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.credit_amount', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.discount_amount', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.total_taxes', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.payment_status', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.from_date', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.end_date', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.bill_from', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.booker_mobile_number', 'LIKE', '%' . $searchTerm . '%')
                    ->orWhere('nonmemberincome.nonmember_id', 'LIKE', '%' . $searchTerm . '%') // Assuming this is safe for LIKE
                    ->orWhere('nonmemberincome.bill_date', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        $count = $obj->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);
        $result = $obj->get();
        $result = json_decode(json_encode($result), true);
        foreach ($result as $key => $value) {
            $result[$key]['payment_detail'] = $this->getNonmemberPaymentDetail($value, 'nonmemberincome', [
                'soc_id' => $value['soc_id'],
                'nonmember_bill_id' => $value['nonmember_bill_id']
            ]);
            $result[$key]['invoice_amount'] = (float)round(($value['bill_amount'] + $value['total_taxes']), 2);
            $result[$key]['paid_amount'] = $result[$key]['payment_detail']['payment_amount'] + $result[$key]['payment_detail']['tds_deducted'];
            $result[$key]['tds_deducted'] = $result[$key]['payment_detail']['tds_deducted'];

            $partial_paid = 0;
            if (!empty($result[$key])) {
                $result[$key]['payment_detail']['payment_amount'] += $result[$key]['payment_detail']['tds_deducted'];
                $partial_paid = round((float)$result[$key]['payment_detail']['payment_amount'] - (float)$result[$key]['advance_amount'], 2);
            }

            $result[$key]['partial_paid'] = ($partial_paid < 0) ? 0 : $partial_paid;
            // check advnace_amount is not empty
            if($result[$key]['advance_amount'] && $result[$key]['advance_amount'] > 0){
                $result[$key]['total_due'] = (float)round(
                    ($result[$key]['invoice_amount']) -
                    ($result[$key]['advance_amount'] + $result[$key]['tds_deducted'] + $result[$key]['payment_detail']['tax_deducted']),
                    2
                );
            } else {
                $result[$key]['total_due'] = (float)round(
                    ($result[$key]['invoice_amount']) -
                    ($result[$key]['paid_amount'] + $result[$key]['tds_deducted'] + $result[$key]['payment_detail']['tax_deducted']),
                    2
                );
            }
            $result[$key]['grand_total'] = round($result[$key]['invoice_amount'], 3);

            //for hide on

            if(!in_array(strtolower($result[$key]['payment_status']),array('paid','cancelled'))){
                $result[$key]['receipt_disable'] = 0;
            }else{
                $result[$key]['receipt_disable'] = 1;
            }
            if(strtolower($result[$key]['payment_status']) != 'cancelled' && strtolower($result[$key]['bill_from']) == 'facility'){
                $result[$key]['cancel_disable1'] = 0;
            }else{
                $result[$key]['cancel_disable1'] = 1;
            }

            // Debugging here will show `payment_detail`

            unset($result[$key]['payment_detail']); // `payment_detail` is removed here
        }
        //dd($result);
        $this->data = $result;
        $this->meta['pagination']['total'] = $count;
    }

    public function getNonmemberPaymentDetail($event, $component, $data = [])
    { // Query NonMemberIncomePayment with the given conditions
        $objNonmemberIncomePayments = IncomeNonmemberBillPayment::where('soc_id', $data['soc_id'])
            ->where('fk_nonmember_bill_id', $data['nonmember_bill_id'])
            ->get();

        $arrPaymentDetail = [
            'payment_amount' => 0,
            'tax_deducted' => 0,
            'tds_deducted' => 0,
            'discount_amount' => 0,
        ];

        if ($objNonmemberIncomePayments->isNotEmpty()) {
            foreach ($objNonmemberIncomePayments as $eachNonmemberPayment) {
                $arrPaymentDetail['bill_number'] = $eachNonmemberPayment->bill_number;
                $arrPaymentDetail['payment_amount'] += (float) round($eachNonmemberPayment->payment_amount, 2);
                $arrPaymentDetail['tax_deducted'] += (float) round($eachNonmemberPayment->tax_deducted, 2);
                $arrPaymentDetail['tds_deducted'] += (float) round($eachNonmemberPayment->tds_deducted, 2);
                $arrPaymentDetail['discount_amount'] += (float) round($eachNonmemberPayment->discount_amount, 2);
            }
        }

        return $arrPaymentDetail;
    }
}
