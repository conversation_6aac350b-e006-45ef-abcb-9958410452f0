<?php

namespace App\Console\Commands\Actions\Income;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class InsertIncomeUnitInvoicesDataSource extends Action
{
    protected $signature = 'datasource:InsertIncomeUnitInvoices {flowId} {parentId} {input}';

    protected $description = 'Insert Income Unit Invoices Data Source';

    protected $rules = [];

    protected $rulesMessage = [];

    public function apply()
    {
        $company_id = $this->input['company_id'];
        if (in_array('all', $this->input['flat_number'])) {
            $unit_name = 'all';
            $building_number = 'all';
        } else {
            $unit_name = $this->getUnitName($this->input['flat_number']);
            $building_number = $this->getBuildingNumber($this->input['flat_number']);
        }

        $obj = $this->tenantDB()->table('income_unit_invoices')
        ->insert([
            'soc_id' => $company_id,
            'invoice_number' => $this->input['invoice_number'],
            'fk_unit_id' => $this->input['unit_id'],
            'unit_name' => $unit_name,
            'fk_unit_ledger_id' => 0,
            'soc_building_id' => 0,
            'soc_building_name' => $building_number,
            'soc_building_floor' => 0,
            'bill_to' => $this->input['member_name'],
            'from_date' => $this->input['from_date'],
            'to_date' => $this->input['to_date'],
            'due_date' => $this->input['due_date'],
            'status' => 'generated',
            'payment_status' => $this->input['payment_status'],
            'cancel_date' => '0000-00-00',
            'cancellation_reason' => '',
            'principal_amount' => $this->input['principal_arrears'] ?? 0,
            'interest_amount' => $this->input['dpc_amount'] ?? 0,
            'advance_amount' => $this->input['advance_amount'] ?? 0,
            'outstanding_principal' => $this->input['principal_arrears'] ?? 0,
            'outstanding_interest' => $this->input['interest_arrears'] ?? 0,
            'roundoff_amount' => $this->input['roundoff_amount'] ?? 0,
            'created_by' => $this->input['user_id'] ?? 0,
            'updated_by' => $this->input['user_id'] ?? 0,
            'created_date' => date('Y-m-d H:i:s'),
            'updated_date' => date('Y-m-d H:i:s'),
            'member_id' => $this->input['member_id'],
        ]);

        // get inserted record after successful insertion
        $income_unit_invoices = $this->tenantDB()->table('income_unit_invoices')
        ->where('invoice_number', $this->input['invoice_number'])
        ->first();

        $this->data = $income_unit_invoices;
    }

    public function getUnitName($flat_number)
    {
        // If flat_number is an array, convert it to string
        if (is_array($flat_number)) {
            $flat_number = implode(', ', $flat_number);
        }

        // Use a regular expression to extract the unit name
        if (preg_match('/\/\s*(.*)$/', $flat_number, $matches)) {
            $unitName = trim($matches[1]); // Trim to remove any leading or trailing spaces
            return $unitName; // Output: "PF303"
        } else {
            // Handle the case where no match is found
            return "";
        }
    }

    public function getBuildingNumber($flat_number)
    {
        // If flat_number is an array, convert it to string
        if (is_array($flat_number)) {
            $flat_number = implode(', ', $flat_number);
        }

        // Use a regular expression to extract the building number
        if (preg_match('/^(Building \d+)/', $flat_number, $matches)) {
            $buildingNumber = $matches[1];
            return $buildingNumber; // Output: "3"
        } else {
            // Handle the case where no match is found
            return "";
        }
    }
}