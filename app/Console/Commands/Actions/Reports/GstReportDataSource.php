<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class GstReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:gstReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the GST list';

    // protected $formatter = [
    //     'id' => '',  
    //     'soc_building_name' => '',
    //     'unit_name' => '',
    //     'gst_number' => '',
    //     'bill_to' => '',
    //     'created_date' => '',
    //     'invoice_number' => '',
    //     'particular' => '',
    //     'amount' => ''
    // ];
    // protected $formatterByKeys = ['id'];

    // protected $mapper = [
    //     'id' => 'id',
    // ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
            $request = $this->input;
            $from_date = date('Y-m-01');
            $to_date = date('Y-m-t');
            $transaction_type = $request['filters']['bill_type'] ?? 'outwards';
    
            if (!empty($this->input['filters'])) {
                $date = $this->getFromAndToDate($this->input['filters']);
                $from_date = $date[0];
                $to_date = $date[1];
            }
            $queryBuilder = $this->tenantDB()->table('chsone_tax_log as taxlog')
            ->select(
                'invoice.unit_invoice_id as id',
                'U.soc_building_name',              // Get from units master
                'U.unit_flat_number as unit_name',                      // Get from units master
                'particular.amount',
                'invoice.created_date',
                'invoice.invoice_number',
                DB::raw("M.gstin AS gst_number"),
                'invoice.member_id',
                'taxlog.particular',
                'taxlog.particular_amount',
                'taxlog.tax_rate',
                'taxlog.tax_rule',
                'taxlog.tax_amount',
                'taxlog.particular_id',
                'U2.soc_building_name as alt_building_name',
                'U2.unit_flat_number as alt_unit_name',
                'ICB.amount as alt_amount',
                'invoice.bill_to',
            )
            // Make sure invoice is joined BEFORE referencing its columns
            ->join('income_unit_invoices as invoice', 'invoice.invoice_number', '=', 'taxlog.invoice_number')
        
            // Join to particular
            ->leftJoin('income_invoice_particular as particular', 'particular.id', '=', 'taxlog.particular_id')
        
            ->leftJoin('income_common_billing_charges as ICB', 'ICB.invoice_number', '=', 'taxlog.invoice_number')
            ->leftJoin('chsone_units_master as U2', 'U2.unit_id', '=', 'ICB.fk_unit_id')

            // Join to chsone_units_master ON invoice.fk_unit_id = U.unit_id
            ->leftJoin('chsone_units_master as U', 'U.unit_id', '=', 'invoice.fk_unit_id')
        
            // Join to members
            ->leftJoin('chsone_members_master as M', 'M.id', '=', 'invoice.member_id')
        
            // Tax rule filtering
            ->where(function ($query) {
                $query->whereIn('taxlog.tax_rule', ['sgst', 'igst'])
                      ->orWhereNull('taxlog.tax_rule');
            })
        
            // Exclude cancelled invoices
            ->where('invoice.status', '!=', 'cancelled');
        
            // add the query for filter
            $queryBuilder->whereBetween('invoice.created_date', [$from_date, $to_date]);
    
            if ($transaction_type == 'inwards') {
                $queryBuilder = $this->tenantDB()->table('chsone_vendor_bill_particulars as particular')
                    ->select(
                        'particular.particulars_id as id',
                        'vendor.vendor_name',
                        'vendor.vendor_service_regn as gst_number',
                        'vendor.place_of_supply',
                        'bills.vendor_bill_num',
                        'bills.vendor_bill_date as created_date',
                        'particular.particular',
                        'particular.amount',
                        'particular.hsn_sac',
                        'particular.tax_cgst',
                        'particular.tax_cgst_amount',
                        'particular.tax_sgst',
                        'particular.tax_sgst_amount',
                        'particular.tax_igst',
                        'bills.vendor_bill_num as invoice_number',
                        'particular.tax_igst_amount',
                        'particular.total_tax',
                        'particular.total_amount',
                    )
                    ->join('chsone_vendor_bill_master as bills', 'bills.vendor_bill_id', '=', 'particular.bill_id')
                    ->join('chsone_vendors_master as vendor', 'vendor.vendor_id', '=', 'bills.vendor_id')
                    ->whereNotIn('bills.status', [0, 3])
                    ->where('bills.is_rcm', '=', 0)
                    ->whereBetween('bills.vendor_bill_date', [$from_date, $to_date]);
                    
                $report = $queryBuilder->get();
                $this->data = $report;
            }
            
            $report = $queryBuilder->get();        
    
            $place_of_supply = $this->masterDB()->table('chsone_societies_master')->select('soc_state')->where('soc_id', $this->input['company_id'])->first();
            
            foreach ($report as $eachReport) {
                $eachReport->place_of_supply = $place_of_supply->soc_state;
                $eachReport->reverse_charge = 'N';
                $eachReport->invoice_type = 'Regular';
                $eachReport->amount = $eachReport->amount ?? $eachReport->alt_amount;
            }
            // Show unit name for incidental invoice end
            $arrIncidentalInvoiceNumber = $report->pluck('invoice_number')->filter(function ($invoiceNumber) {
                return empty($invoiceNumber);
            })->toArray();
    
            $nonmemberinvoiceDetail = $this->tenantDB()->table('income_nonmember_bills as nmi')
                ->select('nmi.gstin', 'N.gstin as gstin1', 'nmi.billed_name', 'nmi.bill_number as invoice_number')
                ->leftJoin('chsone_nonmember_master as N', 'N.nonmember_id', '=', 'nmi.nonmember_id')
                ->whereIn('nmi.bill_number', $arrIncidentalInvoiceNumber)
                ->get()
                ->keyBy('invoice_number');
    
            $invoiceDetail = $this->tenantDB()->table('income_common_billing_charges as ICB')
                ->select('ICB.invoice_number', 'ICB.fk_unit_id', 'U.soc_building_name', 'U.unit_flat_number')
                ->join('chsone_units_master as U', 'U.unit_id', '=', 'ICB.fk_unit_id')
                ->whereIn('ICB.invoice_number', $arrIncidentalInvoiceNumber)
                ->get()
                ->keyBy('invoice_number');
    
            $unitIds = $invoiceDetail->pluck('fk_unit_id')->unique()->toArray();
    
            $memberDetail = $this->tenantDB()->table('chsone_members_master as M')
                ->selectRaw('concat(M.member_first_name," ",M.member_last_name) as name, M.member_type_id, M.fk_unit_id, M.gstin')
                ->whereIn('M.fk_unit_id', $unitIds)
                ->whereIn('M.member_type_id', [1, 2])
                ->where('M.status', '=', 1)
                ->orderBy('M.member_type_id', 'asc')
                ->get()
                ->groupBy('fk_unit_id');
    
            $arrFinalReport = $report->map(function ($eachReport) use ($nonmemberinvoiceDetail, $invoiceDetail, $memberDetail) {
               
                // dd($eachReport);
                if (empty($eachReport->soc_building_name) && $invoiceDetail->has($eachReport->invoice_number)) {
                    // $eachReport->id = $invoiceDetail
                    $invoice = $invoiceDetail->get($eachReport->invoice_number);
                    $eachReport->soc_building_name = $invoice->soc_building_name;
                    $eachReport->unit_name = $invoice->unit_flat_number;
                    $eachReport->bill_to = '';
    
                    if ($memberDetail->has($invoice->fk_unit_id)) {
                        $members = $memberDetail->get($invoice->fk_unit_id);
                        $eachReport->bill_to = $members->pluck('name')->implode(', ');
    
                        $gstNumber = $members->pluck('gstin')->first();
                        if (!empty($gstNumber)) {
                            $eachReport->gst_number = $gstNumber;
                        }
                    }
                }
    
                if ($nonmemberinvoiceDetail->has($eachReport->invoice_number)) {
                    $nonmemberInvoice = $nonmemberinvoiceDetail->get($eachReport->invoice_number);
                    $eachReport->gst_number = $nonmemberInvoice->gstin ?? $nonmemberInvoice->gstin1;
                    $eachReport->bill_to = $nonmemberInvoice->billed_name;
                }
    
                return $eachReport;
            });
            $this->data = $arrFinalReport->toArray();
        } catch (\Exception $e) {
           dd($e);
        }
    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }

}
