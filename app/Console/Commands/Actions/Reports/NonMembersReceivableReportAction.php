<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneNonmemberMaster;

class NonMembersReceivableReportAction extends Action
{
    protected $signature = 'datasource:nonMembersReceivableReport {flowId} {parentId} {input}';

    protected $description = 'Get Non-Member Receivable Report';

    protected $searchDropDown;

    protected $filterDropDown;

    public function apply()
    {
        $data['search_by'] = $this->input['search_by'] ?? null;
        $data['search_value'] = $this->input['search_value'] ?? null;
        $data['filter_by'] = $this->input['filter_by'] ?? null;

        $this->data = $this->nonMembersReceivableReport($data);
        return $this->data;
    }

    /**
     * Compose data to be present in report
     * @return type
     */
    public function nonMembersReceivableReport($data = array())
    {
        $filter['search_by'] = $search_by = $data['search_by'];
        $filter['search_value'] = $search_value = $data['search_value'];
        $filter['filter_by'] = $filter_by = $data['filter_by'];

        $this->searchDropDown = array(null => 'All', 'payment_dues_less' => 'Payment dues less than equal to', 'payment_dues_greater' => 'Payment dues greater than equal to', 'due_date' => 'Due on date');
        $this->filterDropDown = array(
            null => 'Filter By', 'payment_due' => 'Payment due',
            'ledger' => 'Ledger'
        );

        $arrPost['filter'] = $filter;

        //Set Date
        if ($filter['search_by'] == 'due_date' && !empty($filter['search_value'])) {
            $arrPost['filter']['from_date'] = $this->getDisplayDate($filter['search_value']);
        } else {
            $arrPost['filter']['from_date'] = $this->getDisplayDate(date("Y-m-d"));
        }

        $seprateLedger = $this->nonMemberReportQuery($arrPost);
        $commonLedger = $this->nonMemberReportCommonLedgerQuery($arrPost);
        $res = $this->nonmember_array_merge_custom($seprateLedger, $commonLedger);
        $summary = $this->nonMemberSummaryPrepare($res);
        $filters = $this->nonMemberReportFilter($filter);
        $res = $this->nonMemberDataPrepare($res, $filter);

        return $res; // Return the data directly without nesting
    }

    public function nonMemberDataPrepare($nonmembers, $filters)
    {
        $optionsFilter['all'] = $optionsFilter['payment_filter'] = $optionsFilter['ledger_filter'] = $optionsFilter['payment_search'] = false;
        $amountOptionsFilter['less_than'] = $amountOptionsFilter['greater_than'] = false;

        /** Set Filter * */
        if (!empty($filters['search_by']) && !empty($filters['search_value'])) {
            //Search Logic
            if ($filters['search_by'] == 'payment_dues_less') {
                $optionsFilter['payment_search'] = true;
                $amountOptionsFilter['less_than'] = true;
            } elseif ($filters['search_by'] == 'payment_dues_greater') {
                $optionsFilter['payment_search'] = true;
                $amountOptionsFilter['greater_than'] = true;
            }
        } else {
            $optionsFilter['all'] = true;
        }

        if (!empty($filters['filter_by'])) {
            //Filter Logic
            if ($filters['filter_by'] == 'payment_due') {
                $optionsFilter['payment_filter'] = true;
            } elseif ($filters['filter_by'] == 'ledger') {
                $optionsFilter['ledger_filter'] = true;
            }
        }

        /** Remove keys based on set filter * */
        if (!$optionsFilter['all']) {
            if ($amountOptionsFilter['less_than'] || $amountOptionsFilter['greater_than']) {
                //Maintaince Due less than Amount
                if ($amountOptionsFilter['less_than'] && $optionsFilter['payment_search']) {
                    foreach ($nonmembers as $key => $data) {
                        if ($data['due_amount'] > $filters['search_value']) {
                            unset($nonmembers[$key]);
                        }
                    }
                } elseif ($amountOptionsFilter['greater_than'] && $optionsFilter['payment_search']) {
                    foreach ($nonmembers as $key => $data) {
                        if ($data['due_amount'] < $filters['search_value']) {
                            unset($nonmembers[$key]);
                        }
                    }
                }
            }
        }
        //Filter
        if (!empty($filters['filter_by'])) {
            foreach ($nonmembers as $key => $data) {
                if ($optionsFilter['payment_filter']) {
                    unset($nonmembers[$key]['ledger_bal']);
                    unset($nonmembers[$key]['cr_bal']);
                } elseif ($optionsFilter['ledger_filter']) {
                    unset($nonmembers[$key]['cr_bal']);
                    unset($nonmembers[$key]['due_amount']);
                }
            }
        }

        return $nonmembers;
    }

    public function nonMemberReportQuery($data)
    {
        $nonmemberData = $this->nonMemberReportRawQuery($data);
        return $nonmemberData;
    }

    public function nonMemberReportRawQuery($data) {
        $sql = "SELECT CONCAT(a.first_name,' ',COALESCE(a.last_name,'')) as full_name,";

            $sql .= "IF(SUM(IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-IF(b.credit_amount is not null,b.credit_amount,0)-
                IF(d.payment_amount is not null,d.payment_amount,0))<0,0,SUM(IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
                IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0))) as due_amount,
                IF((IF(f.cr_amount is not null,f.cr_amount,0)-IF(f.dr_amount is not null,f.dr_amount,0))<0,0,
		IF(f.cr_amount is not null,f.cr_amount,0)-IF(f.dr_amount is not null,f.dr_amount,0)) as cr_bal,
		IF(h.txn_cr_amount>h.txn_dr_amount,IF(j.nature_of_account != 'cr',-1*(h.txn_cr_amount-h.txn_dr_amount),1*(h.txn_cr_amount-h.txn_dr_amount)),
		IF(j.nature_of_account != 'dr',-1*(h.txn_dr_amount-h.txn_cr_amount),1*(h.txn_dr_amount-h.txn_cr_amount))) as ledger_bal";
            
        $sql .= " FROM chsone_nonmember_master as a
                LEFT JOIN income_nonmember_bills as b
                ON a.nonmember_id = b.nonmember_id AND b.payment_status IN ('unpaid','partialpaid') AND (b.bill_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR b.bill_date is null)
                LEFT JOIN (SELECT c.nonmember_bill_payment_id,c.payment_date,c.fk_nonmember_bill_id,SUM(c.payment_amount) as payment_amount 
                FROM income_nonmember_bill_payments as c  
                WHERE (c.payment_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR c.payment_date is null)
                GROUP BY c.fk_nonmember_bill_id)AS d ON d.fk_nonmember_bill_id = b.nonmember_bill_id " .
                "LEFT JOIN (SELECT e.account_id,e.credit_account_id,SUM(IF(e.transaction_type='cr',e.amount,0)) as cr_amount,
                SUM(IF(e.transaction_type='dr',e.amount,0)) as dr_amount
                FROM chsone_credit_accounts as e WHERE e.account_context = 'nonmember' AND (e.use_credit = 'adjustable' OR e.use_credit is null) AND e.payment_date <= '".$this->getDatabaseDate($data['filter']['from_date'])."'
                GROUP BY e.account_id)AS f ON f.account_id = a.nonmember_id
                LEFT JOIN (SELECT g.transaction_date,SUM(IF(g.transaction_type='cr',g.transaction_amount,0)) as txn_cr_amount,
                SUM(IF(g.transaction_type='dr',g.transaction_amount,0)) as txn_dr_amount,g.ledger_account_id
                FROM chsone_ledger_transactions as g
                WHERE g.transaction_date <= '".$this->getDatabaseDate($data['filter']['from_date'])."'
                GROUP BY g.ledger_account_id)AS h ON h.ledger_account_id = a.nonmember_ledger_id
                LEFT JOIN (SELECT i.ledger_account_id,i.nature_of_account
                FROM chsone_grp_ledger_tree as i)AS j ON j.ledger_account_id = a.nonmember_ledger_id";

        if (isset($data['filter']['from_date']) && !empty($data['filter']['from_date'])) {
            $sql .= " WHERE a.is_default_ledger != '1' AND (b.bill_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR b.bill_date is null)";
        }
        elseif ((isset($data['filter']['amount_options']) && $data['filter']['amount_options'] == '<=') && (isset($data['filter']['amount']) && !empty($data['filter']['amount']))) {
            $sql .= "  WHERE a.is_default_ledger != '1' AND IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
		IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0) <= " . $data['filter']['amount'];
        }
        elseif ((isset($data['filter']['amount_options']) && $data['filter']['amount_options'] == '>=') && (isset($data['filter']['amount']) && !empty($data['filter']['amount']))) {
            $sql .= "  WHERE a.is_default_ledger != '1' AND IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
		IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0) >= " . $data['filter']['amount'];
        }else{
            $sql .= " WHERE a.is_default_ledger != '1'";
        }

        $sql .= " GROUP BY a.nonmember_id ORDER BY a.nonmember_id DESC";

        $sql .= ';';

        return $this->tenantDB()->select($sql);
    }

    public function nonMemberReportCommonLedgerQuery($data)
    {
        $nonmemberData = $this->nonMemberCommonLedgerReportRawQuery($data);
        $commonLedger = array('full_name' => 'Common nonmember', 'ledger_bal' => 0, 'due_amount' => 0, 'cr_bal' => 0);
        $nonmemberData = json_decode(json_encode($nonmemberData), true);
        foreach ($nonmemberData as $value) {
            $commonLedger['due_amount'] += $value['due_amount'];
            $commonLedger['cr_bal'] += $value['cr_bal'];
            $commonLedger['ledger_bal'] += $value['ledger_bal'];
        }
        return $commonLedger;
    }

    public function nonMemberCommonLedgerReportRawQuery($data) {
        $sql = "SELECT CONCAT(a.first_name,' ',COALESCE(a.last_name,'')) as full_name,";

            $sql .= "IF(SUM(IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-IF(b.credit_amount is not null,b.credit_amount,0)-
                IF(d.payment_amount is not null,d.payment_amount,0))<0,0,SUM(IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
                IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0))) as due_amount,
                IF((IF(f.cr_amount is not null,f.cr_amount,0)-IF(f.dr_amount is not null,f.dr_amount,0))<0,0,
		IF(f.cr_amount is not null,f.cr_amount,0)-IF(f.dr_amount is not null,f.dr_amount,0)) as cr_bal,
		IF(h.txn_cr_amount>h.txn_dr_amount,IF(j.nature_of_account != 'cr',-1*(h.txn_cr_amount-h.txn_dr_amount),1*(h.txn_cr_amount-h.txn_dr_amount)),
		IF(j.nature_of_account != 'dr',-1*(h.txn_dr_amount-h.txn_cr_amount),1*(h.txn_dr_amount-h.txn_cr_amount))) as ledger_bal";
            
        $sql .= " FROM chsone_nonmember_master as a
                LEFT JOIN income_nonmember_bills as b
                ON a.nonmember_id = b.nonmember_id AND b.payment_status IN ('unpaid','partialpaid') AND (b.bill_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR b.bill_date is null)
                LEFT JOIN (SELECT c.nonmember_bill_payment_id,c.payment_date,c.fk_nonmember_bill_id,SUM(c.payment_amount) as payment_amount 
                FROM income_nonmember_bill_payments as c  
                WHERE (c.payment_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR c.payment_date is null)
                GROUP BY c.fk_nonmember_bill_id)AS d ON d.fk_nonmember_bill_id = b.nonmember_bill_id " .
                "LEFT JOIN (SELECT e.account_id,e.credit_account_id,SUM(IF(e.transaction_type='cr',e.amount,0)) as cr_amount,
                SUM(IF(e.transaction_type='dr',e.amount,0)) as dr_amount
                FROM chsone_credit_accounts as e WHERE e.account_context = 'nonmember' AND (e.use_credit = 'adjustable' OR e.use_credit is null) AND e.payment_date <= '".$this->getDatabaseDate($data['filter']['from_date'])."'
                GROUP BY e.account_id)AS f ON f.account_id = a.nonmember_id
                LEFT JOIN (SELECT g.transaction_date,SUM(IF(g.transaction_type='cr',g.transaction_amount,0)) as txn_cr_amount,
                SUM(IF(g.transaction_type='dr',g.transaction_amount,0)) as txn_dr_amount,g.ledger_account_id
                FROM chsone_ledger_transactions as g
                WHERE g.transaction_date <= '".$this->getDatabaseDate($data['filter']['from_date'])."'
                GROUP BY g.ledger_account_id)AS h ON h.ledger_account_id = a.nonmember_ledger_id
                LEFT JOIN (SELECT i.ledger_account_id,i.nature_of_account
                FROM chsone_grp_ledger_tree as i)AS j ON j.ledger_account_id = a.nonmember_ledger_id";

        if (isset($data['filter']['from_date']) && !empty($data['filter']['from_date'])) {
            $sql .= " WHERE a.is_default_ledger = '1' AND (b.bill_date <= '" . $this->getDatabaseDate($data['filter']['from_date']) . "' OR b.bill_date is null)";
        }
        elseif ((isset($data['filter']['amount_options']) && $data['filter']['amount_options'] == '<=') && (isset($data['filter']['amount']) && !empty($data['filter']['amount']))) {
            $sql .= "  WHERE a.is_default_ledger = '1' AND IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
		IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0) <= " . $data['filter']['amount'];
        }
        elseif ((isset($data['filter']['amount_options']) && $data['filter']['amount_options'] == '>=') && (isset($data['filter']['amount']) && !empty($data['filter']['amount']))) {
            $sql .= "  WHERE a.is_default_ledger = '1' AND IF(b.bill_amount is not null,b.bill_amount,0)+IF(b.total_taxes is not null,b.total_taxes,0)-
		IF(b.credit_amount is not null,b.credit_amount,0)-IF(d.payment_amount is not null,d.payment_amount,0) >= " . $data['filter']['amount'];
        }else{
            $sql .= " WHERE a.is_default_ledger = '1'";
        }

        $sql .= " GROUP BY a.nonmember_id ORDER BY a.nonmember_id DESC";

        $sql .= ';';

        return $this->tenantDB()->select($sql);
    }

    function nonmember_array_merge_custom($result, $commonLedger)
    {
        $count = count($result);
        $result[$count]['full_name'] = $commonLedger['full_name'];
        $result[$count]['due_amount'] = $commonLedger['due_amount'];
        $result[$count]['cr_bal'] = $commonLedger['cr_bal'];
        $result[$count]['ledger_bal'] = $commonLedger['ledger_bal'];
        return $result;
    }

    public function nonMemberSummaryPrepare($nonmembers)
    {

        $summary = array();
        $summary['ledger_bal'] = 0;
        $summary['due_bal'] = 0;
        $summary['credit_bal'] = 0;
        $nonmembers = json_decode(json_encode($nonmembers), true);
        foreach ($nonmembers as $data) {
            $summary['credit_bal'] = (float) $summary['credit_bal'] + (float) $data['cr_bal'];
            $summary['due_bal'] = (float) $summary['due_bal'] + (float) $data['due_amount'];
            $summary['ledger_bal'] = (float) $summary['ledger_bal'] + (float) $data['ledger_bal'];
        }
        return $summary;
    }

    public function nonMemberReportFilter($filters)
    {
        $filterData = array();
        if (!empty($filters['search_by']) && !empty($filters['search_value'])) {
            //Search Logic
            if ($filters['search_by'] == 'payment_dues_less') {
                $filterData['Payment due'] = ' <= ' . $filters['search_value'] . ' , ';
            } elseif ($filters['search_by'] == 'payment_dues_greater') {
                $filterData['Payment due'] = ' >= ' . $filters['search_value'] . ' , ';
            } elseif ($filters['search_by'] == 'due_date') {
                $filterData['Due as on'] = $filters['search_value'] . ' , ';
            }
        }
        if (!empty($filters['filter_by'])) {
            //Filter Logic
            if ($filters['filter_by'] == 'payment_due') {
                $filterData['Filter By'] = 'Payment due , ';
            } elseif ($filters['filter_by'] == 'ledger') {
                $filterData['Filter By'] = ' Ledger , ';
            }
        }

        if (!empty($filterData)) {
            $keys = array_keys($filterData);
            $last = end($keys);
            $filterData[$last] = rtrim($filterData[$last], ' , ');
        }
        return $filterData;
    }
}
