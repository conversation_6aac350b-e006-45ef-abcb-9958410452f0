<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Support\Facades\DB;
use App\Console\Commands\Action;

class IncidentalInvoiceDetailReportDataSource extends Action
{
    protected $signature = 'datasource:incidentalInvoiceDetailReport  {flowId} {parentId} {input}';
    protected $description = 'Incidental Invoice Detail Report Data Source';

    public function apply()
    {
        try {
            $companyId = $this->input['company_id'];
            // Pending invoice date filtration
            $invoiceDate = $this->input['filters']['invoice_date'] ?? date('Y-m-d');

            // First, get all charge types
            $chargeTypes = $this->tenantDB()->table('income_common_area_charges')
                ->where('soc_id', $companyId)
                ->select('id', 'particular')
                ->get();

            // Build dynamic select array
            $selectArray = [
                DB::raw('CONCAT(um.soc_building_name, " ", um.unit_flat_number) as unit_name'),
                'icb.fk_unit_id as fk_unit_id',
                DB::raw('CONCAT(mm.member_first_name, " ", mm.member_last_name) as member_name'),
                'icb.invoice_number as invoice_number',
                'icb.fk_member_id as fk_member_id',
            ];

            // Add dynamic CASE statements for each charge type
            foreach ($chargeTypes as $type) {
                // Sanitize column name - remove special characters and spaces
                $columnName = preg_replace('/[^a-zA-Z0-9]/', '_', $type->particular);
                $columnName = strtolower(trim($columnName, '_'));
                
                $selectArray[] = DB::raw("SUM(CASE WHEN icb.billing_type = {$type->id} THEN icb.amount ELSE 0 END) as `{$columnName}`");
            }

            // Add remaining static columns
            $selectArray = array_merge($selectArray, [
                'iui.interest_amount as interest',
                DB::raw('0 as tax'),
                'icb.amount as invoice_amount',
                DB::raw('0 as credit_adjustment'),
                'iui.outstanding_principal as p_arrears',
                'iui.outstanding_interest as i_arrears',
                DB::raw('(icb.amount + COALESCE(iui.outstanding_principal, 0) + COALESCE(iui.outstanding_interest, 0)) as net_due')
            ]);

            $billingTypes = $this->tenantDB()->table('income_common_area_charges')
            ->where('soc_id', $companyId)
            ->pluck('id')
            ->toArray();
        
        // Fetch all raw rows, one per particular per invoice per unit per member
            $rows = $this->tenantDB()->table('income_common_billing_charges as icb')
                ->leftJoin('income_common_area_charges as c', 'icb.billing_type', '=', 'c.id')
                ->leftJoin('chsone_units_master as um', 'icb.fk_unit_id', '=', 'um.unit_id')
                ->leftJoin('chsone_members_master as mm', 'icb.fk_member_id', '=', 'mm.id')
                ->leftJoin('income_unit_invoices as iui', 'icb.invoice_number', '=', 'iui.invoice_number')
                ->select([
                    DB::raw('CONCAT(um.soc_building_name, " ", um.unit_flat_number) as unit_name'),
                    'um.unit_id',
                    DB::raw('CONCAT(mm.member_first_name, " ", mm.member_last_name) as member_name'),
                    'mm.id as member_id',
                    'icb.invoice_number',
                    'iui.outstanding_principal as p_arrears',
                    'iui.outstanding_interest as i_arrears',
                    'icb.fk_unit_id',
                    'icb.fk_member_id',
                    'icb.billing_type',
                    'icb.amount as particular_amount',
                    'c.particular as particular_name',
                    'iui.interest_amount',
                    'icb.created_date',
                    'icb.payment_status',
                ])
                ->where('icb.soc_id', $companyId)
                ->whereIn('icb.billing_type', $billingTypes)
                ->where('icb.from_date', '<=', $invoiceDate)
                ->where('icb.payment_status', '!=', 'cancelled')
                ->orderBy('icb.created_date', 'asc')
                ->groupBy('icb.billing_type', 'icb.fk_unit_id', 'icb.fk_member_id')
                ->selectRaw('MAX(icb.created_date) as latest_date')
                ->get();
            
            // Mapping from particular_name to result key
        $particularKeyMap = [
            'Move in' => 'move_in',
            'Move out' => 'move_out',
            'Renovation' => 'renovation',
            'Non Occupancy Charges - Arrears' => 'non_occupancy_charges___arrears',
            'payment reversal correction' => 'payment_reversal_correction',
            'Transfer Charges' => 'transfer_charges',
            'Share Transfer Fee' => 'share_transfer_fee',
            'Entrance - Membership Fees' => 'entrance___membership_fees',
            'Cheque Return Charges' => 'cheque_return_charges',
            'Dues of Use of Society Premises' => 'dues_of_use_of_society_premises',
            'Reimbursement of Legal Expenses' => 'reimbursement_of_legal_expenses',
            'Interest on Late Payment' => 'interest_on_late_payment',
            'Interest' => 'interest',
            'Tax' => 'tax',
            'Invoice Amount' => 'invoice_amount',
            'Credit/Adjustment' => 'credit_adjustment',
            'P Arrears' => 'p_arrears',
            'I Arrears' => 'i_arrears',
            'Net Due' => 'net_due',
        ];

        // Convert $rows to array if it's a collection
        $invoiceKeys = [
            'move_in', 'move_out', 'renovation', 'non_occupancy_charges___arrears',
            'payment_reversal_correction', 'transfer_charges', 'share_transfer_fee',
            'entrance___membership_fees', 'cheque_return_charges', 'dues_of_use_of_society_premises',
            'reimbursement_of_legal_expenses', 'interest_on_late_payment', 'interest', 'tax'
        ];
        $result = $rows->map(function ($row) use ($particularKeyMap, $invoiceKeys) {
            $rowArr = (array) $row;
            $particularName = $rowArr['particular_name'] ?? null;
            $particularAmount = $rowArr['particular_amount'] ?? null;
            if ($particularName && isset($particularKeyMap[$particularName])) {
                $rowArr[$particularKeyMap[$particularName]] = $particularAmount;
            }
            // Calculate invoice_amount as sum of invoiceKeys
            $invoiceAmount = 0;
            foreach ($invoiceKeys as $key) {
                $val = $rowArr[$key] ?? 0;
                if (is_numeric($val)) {
                    $invoiceAmount += $val;
                }
            }
            $rowArr['invoice_amount'] = $invoiceAmount;
            // Calculate net_due = invoice_amount - credit_adjustment + p_arrears + i_arrears
            $creditAdjustment = isset($rowArr['credit_adjustment']) ? floatval($rowArr['credit_adjustment']) : 0;
            $pArrears = isset($rowArr['p_arrears']) ? floatval($rowArr['p_arrears']) : 0;
            $iArrears = isset($rowArr['i_arrears']) ? floatval($rowArr['i_arrears']) : 0;
            $rowArr['net_due'] = $invoiceAmount - $creditAdjustment + $pArrears + $iArrears;
            return $rowArr;
        });
            // Build dynamic headers using original particulars for display
            $headers = ['Unit Name', 'Member Name', 'Invoice Number'];
            foreach ($chargeTypes as $type) {
                $headers[] = $type->particular;
            }
            $headers = array_merge($headers, [
                'Interest', 'Tax', 'Invoice Amount', 'Credit/Adjustment',
                'P Arrears', 'I Arrears', 'Net Due'
            ]);

            $summary = [
                'invoice_count' => $result->count(),
                'date' => $invoiceDate,
                'total_payable' => number_format($result->sum('net_due'), 2)
            ];
            
                $this->data = [
                   $result,
                    [$summary]
                ];
        } catch (\Exception $e) {
           dd('IncidentalInvoiceDetailReport Error: ' . $e->getMessage());
        }
    }
}
