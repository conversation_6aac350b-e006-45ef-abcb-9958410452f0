<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class ReceiptListReportDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:ReceiptListReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the receipts list';

    protected $formatter = [
        'id' => '',
        'payment_date' => '',
        'receipt_number' => '',
        'payment_mode' => '',
        'received_from'   => '',
        'invoice_number' => '',
        'building_unit' => 'concat:soc_building_name,unit_flat_number',
        'payment_amount' => '',
        'transaction_status' => '',
        'payment_reference' => 'concatpaymentref:payment_mode,transaction_reference,payment_instrument'
    ];

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        'id' => 'id',
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $society_id = $this->input['company_id'];
        $from_date = date('Y-m-01');
        $to_date = date('Y-m-t');
    
        if (!empty($this->input['filters'])) {
            $date = $this->getFromAndToDate($this->input['filters']);
            $from_date = $date[0];
            $to_date = $date[1];
        }
    
        $filters = $this->input['filters'] ?? [];
    
        // Start building query
        $query = "SELECT ipt.*, um.soc_building_name, um.unit_flat_number, 
            CONCAT(um.soc_building_name, '/', um.unit_flat_number) as society_unit_name,
            CASE WHEN ipt.payment_mode = 'cashtransfer' THEN 'Electronic Fund Transfer' ELSE ipt.payment_mode END as payment_mode,
            CASE WHEN ipt.status = 'P' AND ipt.transaction_status = 'complete' THEN 'Submitted' ELSE 'Cleared' END as transaction_status
            FROM income_invoice_payment_tracker ipt
            INNER JOIN chsone_units_master um ON um.unit_id = ipt.unit_id
            WHERE ipt.soc_id = $society_id
            AND payment_date BETWEEN '$from_date' AND '$to_date'";
    
        // Dynamically add filters
        if (!empty($filters['receipt_number'])) {
            $receiptNumber = addslashes($filters['receipt_number']);
            $query .= " AND ipt.receipt_number LIKE '%$receiptNumber%'";
        }
    
        if (!empty($filters['transaction_reference'])) {
            $transactionReference = addslashes($filters['transaction_reference']);
            $query .= " AND ipt.transaction_reference LIKE '%$transactionReference%'";
        }
    
        if (!empty($filters['payment_date'])) {
            $paymentDate = addslashes($filters['payment_date']);
            $query .= " AND DATE(ipt.payment_date) = '$paymentDate'";
        }

        if (!empty($filters['payment_mode'])) {
            $paymentMode = addslashes($filters['payment_mode']);
            $query .= " AND ipt.payment_mode = '$paymentMode'";
        }
    
        if (!empty($filters['invoice_number'])) {
            $invoiceNumber = addslashes($filters['invoice_number']);
            $query .= " AND ipt.invoice_number LIKE '%$invoiceNumber%'";
        }
    
        if (!empty($filters['unit_flat_number'])) {
            $unitFlatNumber = addslashes($filters['unit_flat_number']);
            $query .= " AND um.unit_flat_number LIKE '%$unitFlatNumber%'";
        }

        if (!empty($filters['bill_type'])) {
            $billType = addslashes($filters['bill_type']);
            $query .= " AND ipt.bill_type = '$billType'";
        }

        if (!empty($filters['status'])) {
            $status = addslashes($filters['status']);
            $query .= " AND ipt.status = '$status'";
        }
    
        if (!empty($filters['member_name'])) {
            $memberName = addslashes($filters['member_name']);
            $query .= " AND ipt.received_from LIKE '%$memberName%'";
        }

        if (!empty($filters['unit_flat_number'])) {
            $unitFlatNumber = addslashes($filters['unit_flat_number']);
            $query .= " AND um.unit_flat_number LIKE '%$unitFlatNumber%'";
        }
    
        $query .= " ORDER BY ipt.payment_date DESC";
    
        // Execute and format
        $result = $this->tenantDB()->select($query);
        $summary = [
            'paid_amount_total' => array_sum(array_column($result, 'payment_amount')),
            'tds_deducted_total' => array_sum(array_column($result, 'tds_deducted')),
            'write_off_total' => array_sum(array_column($result, 'writeoff_amount')),
        ];

        $final = [
            $result,
            [$summary]
        ];
        $this->data = $final;
        $this->summary = $summary;
    }    

    public function concat($soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . ' / ' . $unit_flat_number;
    }

    public function concatpaymentref($payment_mode, $transaction_reference, $payment_intstrument){
        if($payment_mode == 'cheque'){
            return $payment_mode . ':' . $transaction_reference . ' / ' . $payment_intstrument;
        }else{
            return $transaction_reference;
        }
    }

    public function getFromAndToDate($inputs){
        // $date = $inputs['transaction_date'];
        // $dateRange = explode(',', $date);
        $dateRange = $inputs;
        $from_date = $dateRange['startDate'] ?? date("Y-m-01");
        $to_date = $dateRange['endDate'] ?? date("Y-m-t");
        return array($from_date, $to_date);
    }
}
