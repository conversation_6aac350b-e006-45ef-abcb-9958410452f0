<?php

namespace App\Console\Commands\Actions\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class IRegisterAction extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:iRegister {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'I Register Report List';

    protected $formatterByKeys = ['id'];

    protected $mapper = [
        "id" => "share_cert.id"
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unitId = $this->input['unitId'];
        $arrayMaster = [];
        $arrayMaster['iRegister'] = $this->tenantDB()->table('chsone_member_share_certificate AS share_cert')
                    ->selectRaw("*")
                    ->where('share_cert.status', "1")
                    ->where('share_cert.unit_id', $unitId)
                    ->get()->toArray()[0];

        $regId = $arrayMaster['iRegister']->id;

        $arrayMaster['nominees'] = $this->tenantDB()->table('chsone_iregister_nominees AS nominees')
                    ->selectRaw("*")
                    ->where('nominees.status', "1")
                    ->where('nominees.share_certificate_id', $regId)
                    ->get()->toArray();

        $arrayMaster['cashBookFolio'] = $this->tenantDB()->table('chsone_cashbook_folios AS cashBookFolio')
                    ->select(
                        'cashBookFolio.id',
                        'cashBookFolio.share_certificate_id',
                        'cashBookFolio.cashbook_folio_no',
                        'cashBookFolio.source_cashbook_folio_id',
                        'cashBookFolio.created_date',
                        'cshBkFolio.cashbook_folio_no AS source_cashbook_folio_no'
                    )
                    ->leftJoin(
                        'chsone_cashbook_folios AS cshBkFolio',
                        'cshBkFolio.id',
                        '=',
                        'cashBookFolio.source_cashbook_folio_id'
                    )
                    ->where('cashBookFolio.share_certificate_id', $regId)
                    ->orderByDesc('cashBookFolio.created_date')
                    ->get()->toArray();
        $this->data[] = $arrayMaster;
    }
}
