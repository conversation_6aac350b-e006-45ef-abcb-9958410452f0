<?php

namespace App\Console\Commands\Actions\Settings\UnitCategories;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DefineUnitTypeDataSource extends Action
{
    protected $signature = 'datasource:defineUnitType {flowId} {parentId} {input}';

    protected $description = 'Create Unit Type';

    public function apply()
    {
        $societyId = $this->input['company_id'];

        $unitCategories = $this->input['params'];


        if(count($unitCategories) > 1)
        {

        foreach ($unitCategories as &$unitCategory) {
            $unitCategory['soc_id'] = $societyId;
            $unitCategory['status'] = 1;
            $unitCategory['created_date'] = date('Y-m-d H:i:s');
            $unitCategory['created_by'] = $unitCategory['created_by'] ?? $this->input['created_by'];
            $unitCategory['updated_date'] = date('Y-m-d H:i:s');
            $unitCategory['updated_by'] = $unitCategory['updated_by'] ?? $this->input['updated_by'];
            $unitCategory['chargeable'] = $unitCategory['chargeable'] ?? 0;
            $unitCategory['water_inlets_num'] = $unitCategory['water_inlets_num'] ?? 0;
        }
    }

        $obj = $this->tenantDB()->table("chsone_society_units_tpl")
                    ->insert($unitCategories);

        if($obj)
        {
            $this->status = 'success';
            $this->message = 'Unit Type created successfully';
            $this->statusCode = 200;
        }
        else
        {
            $this->status = 'error';
            $this->message = 'Unable to create Unit Type';
            $this->statusCode = 400;
        }
    }
}
