<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class ReportsController extends Controller
{
    public function tdsreceivableReport(Request $request)
    {
        return $this->workflow('workflow:tdsreceivableReport', $request->all());
    }

    public function tdspayableReport(Request $request)
    {
        return $this->workflow('workflow:tdspayableReport', $request->all());
    }

    public function downloadTdsPayableReport(Request $request, $type)
    {
        $request = $request->merge(["type" => $type]);
        return $this->workflow('workflow:downloadTdsPayableReport', $request->all());
    }

    public function memberAdvances(Request $request)
    {
        return $this->workflow('workflow:advanceMemberReport', $request->all());
    }

    public function nonMemberAdvances(Request $request)
    {
        return $this->workflow('workflow:advanceMemberReport', $request->all());
    }

    public function parkingAllottmentDetailReportPrint(Request $request)
    {
        return $this->workflow('workflow:parkingAllotmentDetailReport', $request->all());
    }

    public function allottedVehicleDetailReportPrint(Request $request)
    {
        return $this->workflow('workflow:allottedVehicleDetailReport', $request->all());
    }

    public function allottedVehicleCountDetailReport(Request $request)
    {
        return $this->workflow('workflow:allottedVehicleCountDetailReport', $request->all());
    }

    public function allottedVehicleCountDetailReportDownload(Request $request, $type)
    {
        $request->merge(['type' => $type]);
        return $this->workflow('workflow:allottedVehicleCountDetailReportDownload', $request->all());
    }

    public function voucherReport(Request $request)
    {
        return $this->workflow('workflow:voucherReport', $request->all());
    }

    public function receiptReport(Request $request)
    {
        return $this->workflow('workflow:receiptReport', $request->all());
    }

    public function downloadTdsReceivableReport(Request $request, $type)
    {
        $request = $request->merge(["type" => $type]);
        return $this->workflow('workflow:downloadTdsReceivableReport', $request->all());
    }

    public function gstReport(Request $request)
    {
        return $this->workflow('workflow:gstReport', $request->all());
    }

    public function downloadGstReport(Request $request, $type)
    {
        $request = $request->merge(["type" => $type]);
        return $this->workflow('workflow:downloadGstReport', $request->all());
    }

    public function voucherReportDownload(Request $request, $type)
    {
        $request->merge([
            "type" => $type
        ]);
        return $this->workflow('workflow:voucherReportDownload', $request->all());
    }

    public function downloadReceiptReport(Request $request, $type)
    {
        $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:downloadReceiptReport', $request->all());
    }

    public function nonMemberAdvancesDownload(Request $request, $type)
    {
        $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:nonMemberAdvancesDownload', $request->all());
    }

    public function memberAdvancesDownload(Request $request, $type)
    {
        $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:advanceMemberReportDownload', $request->all());
    }

    public function parkingAllottmentDetailReportPrintDownload(Request $request, $type)
    {
        $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:parkingAllottmentDetailReportPrintDownload', $request->all());
    }

    public function incidentalReceivableReport(Request $request)
    {
        return $this->workflow('workflow:incidentalReceivableReport', $request->all());
    }

    public function expenseReport(Request $request)
    {
        return $this->workflow('workflow:expenseReport', $request->all());
    }

    public function expensePaymentReport(Request $request)
    {
        return $this->workflow('workflow:expensePaymentReport', $request->all());
    }

    /**
     * Download Expense Payment Report
     *
     * @param string $type (xlsx|pdf)
     * @return \Illuminate\Http\Response
     */
    public function expensePaymentDownloadReport(Request $request, $type)
    {
        $request->merge(['type' => $type]);
        return $this->workflow('workflow:downloadExpensePaymentReport', $request->all());
    }

    /**
     * Get Expense Budget Report
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function expenseBudgetReport(Request $request)
    {
        return $this->workflow('workflow:expenseBudgetReport', $request->all());
    }

    /**
     * Download Expense Budget Report
     *
     * @param string $type (xlsx|pdf)
     * @return \Illuminate\Http\Response
     */
    public function downloadExpenseBudgetReport(Request $request, $type)
    {
        $request = $request->merge([
            "type" => $type,
            "download" => true
        ]);
        
        return $this->workflow('workflow:downloadExpenseBudgetReport', $request->all());
    }

    public function bankRecoReport(Request $request)
    {
        return $this->workflow('workflow:bankRecoReport', $request->all());
    }

    /**
     * Download Bank Reconciliation Report
     *
     * @param string $type (excel|pdf)
     * @return \Illuminate\Http\Response
     */
    public function bankRecoReportDownload(Request $request, $type)
    {
        $request = $request->merge([
            'type' => $type,
            'download' => true
        ]);
        return $this->workflow('workflow:bankRecoReportDownload', $request->all());
    }

    /**
     * Download Member Receipt Report
     *
     * @param string $type (xlsx|pdf)
     * @return \Illuminate\Http\Response
     */
    public function downloadMemberReceiptReport(Request $request, $type)
    {
        $request->merge([
            'type' => $type,
            'download' => true
        ]);
        
        return $this->workflow('workflow:memberReceiptReport', $request->all());
    }

    public function nonMembersReceivable(Request $request)
    {
        return $this->workflow('workflow:nonMembersReceivableReport', $request->all());
    }

    public function nonMembersReceivableDownload(Request $request, $type)
    {
        $request->merge([
            'type' => $type,
            'download' => true
        ]);
        return $this->workflow('workflow:nonMembersReceivableReportDownload', $request->all());
    }
}
